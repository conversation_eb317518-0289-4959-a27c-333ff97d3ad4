import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
  Alert,
  Platform,
  Modal,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useProfileStore } from '@/stores/profileStore';
import { theme } from '@/constants/theme';
import RangePicker from '../../components/ui/RangeSlider';
import { triggerHaptic } from '../../utils/haptics';
import TimePicker from '../../components/ui/TimePicker';
import DistancePicker from '../../components/ui/DistancePicker';
import SessionTimeoutPicker from '../../components/ui/SessionTimeoutPicker';
import SettingsModal from '../../components/ui/SettingsModal';
import SettingsSearch from '../../components/ui/SettingsSearch';
import SettingsSyncManager from '../../components/settings/SettingsSyncManager';
import WebCompatibilityTest from '../../components/test/WebCompatibilityTest';
import LoadingSkeleton from '../../components/LoadingSkeleton';
import {
  ArrowLeft,
  Heart,
  Shield,
  Bell,
  User,
  MapPin,
  Eye,
  MessageCircle,
  Moon,
  Vibrate,
  Volume2,
  Trash2,
  ChevronRight,
  Settings,
  Award,
  Info,
  Search,
  Download,
  Upload,
  RotateCcw,
  Filter,
  Clock,
  Check,
  X,
  TestTube,
} from 'lucide-react-native';

export default function ProfileSettingsScreen() {
  const router = useRouter();
  const { settings, updateSettings, isUpdating, isLoading } = useProfileStore();

  const [localSettings, setLocalSettings] = useState({
    // Dating preferences
    ageMin: 22,
    ageMax: 35,
    maxDistance: 50,
    genderPreference: 'everyone' as 'men' | 'women' | 'everyone',
    
    // Privacy settings
    profileVisibility: 'public' as 'public' | 'private' | 'friends',
    showDistance: true,
    showAge: true,
    showLastSeen: false,
    allowMessagesFrom: 'matches' as 'everyone' | 'matches' | 'premium',
    showOnlineStatus: true,
    incognitoMode: false,
    
    // Notification settings
    pushNotifications: true,
    emailNotifications: false,
    newMatches: true,
    newMessages: true,
    likes: true,
    superLikes: true,
    promotions: false,
    tips: true,
    soundEnabled: true,
    vibrationEnabled: true,
    quietHoursEnabled: false,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    
    // Account settings
    twoFactorAuth: false,
    loginAlerts: true,
    dataSharing: false,
    analytics: true,
    locationServices: true,
    autoRenewal: false,
  });

  // Modal states
  const [showAgeRangeModal, setShowAgeRangeModal] = useState(false);
  const [showDistanceModal, setShowDistanceModal] = useState(false);
  const [showGenderModal, setShowGenderModal] = useState(false);
  const [showQuietHoursModal, setShowQuietHoursModal] = useState(false);
  const [showSessionTimeoutModal, setShowSessionTimeoutModal] = useState(false);

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [showWebTest, setShowWebTest] = useState(false);

  // Temporary values for modals
  const [tempAgeRange, setTempAgeRange] = useState({ min: 22, max: 35 });
  const [tempDistance, setTempDistance] = useState(50);
  const [tempGender, setTempGender] = useState('everyone');
  const [tempQuietHours, setTempQuietHours] = useState({ start: '22:00', end: '08:00' });
  const [tempSessionTimeout, setTempSessionTimeout] = useState(30);

  const settingsCategories = [
    'Dating Preferences',
    'Privacy & Safety',
    'Notifications',
    'Account & Security',
    'Advanced'
  ];

  useEffect(() => {
    if (settings) {
      setLocalSettings({
        ageMin: settings.dating.ageRange.min,
        ageMax: settings.dating.ageRange.max,
        maxDistance: settings.dating.maxDistance,
        genderPreference: settings.dating.genderPreference,
        profileVisibility: settings.privacy.profileVisibility,
        showDistance: settings.privacy.showDistance,
        showAge: settings.privacy.showAge,
        showLastSeen: settings.privacy.showLastSeen,
        allowMessagesFrom: settings.privacy.allowMessagesFrom,
        showOnlineStatus: settings.privacy.showOnlineStatus,
        incognitoMode: settings.privacy.incognitoMode,
        pushNotifications: settings.notifications.pushNotifications,
        emailNotifications: settings.notifications.emailNotifications,
        newMatches: settings.notifications.newMatches,
        newMessages: settings.notifications.newMessages,
        likes: settings.notifications.likes,
        superLikes: settings.notifications.superLikes,
        promotions: settings.notifications.promotions,
        tips: settings.notifications.tips,
        soundEnabled: settings.notifications.soundEnabled,
        vibrationEnabled: settings.notifications.vibrationEnabled,
        quietHoursEnabled: settings.notifications.quietHours.enabled,
        quietHoursStart: settings.notifications.quietHours.startTime,
        quietHoursEnd: settings.notifications.quietHours.endTime,
        twoFactorAuth: settings.account.twoFactorAuth,
        loginAlerts: settings.account.loginAlerts,
        dataSharing: settings.account.dataSharing,
        analytics: settings.account.analytics,
        locationServices: settings.account.locationServices,
        autoRenewal: settings.account.autoRenewal,
      });
    }
  }, [settings]);

  const handleToggle = async (key: string, value: boolean) => {
    triggerHaptic.light();

    setLocalSettings(prev => ({ ...prev, [key]: value }));

    // Update the store immediately for better UX
    if (settings) {
      const updatedSettings = { ...settings };
      
      // Map the local setting to the correct nested structure
      if (key === 'ageMin' || key === 'ageMax') {
        updatedSettings.dating.ageRange = {
          min: key === 'ageMin' ? value as any : localSettings.ageMin,
          max: key === 'ageMax' ? value as any : localSettings.ageMax,
        };
      } else if (key === 'maxDistance' || key === 'genderPreference') {
        (updatedSettings.dating as any)[key] = value;
      } else if (['profileVisibility', 'showDistance', 'showAge', 'showLastSeen', 'allowMessagesFrom', 'showOnlineStatus', 'incognitoMode'].includes(key)) {
        (updatedSettings.privacy as any)[key] = value;
      } else if (['pushNotifications', 'emailNotifications', 'newMatches', 'newMessages', 'likes', 'superLikes', 'promotions', 'tips', 'soundEnabled', 'vibrationEnabled'].includes(key)) {
        (updatedSettings.notifications as any)[key] = value;
      } else if (key === 'quietHoursEnabled') {
        updatedSettings.notifications.quietHours.enabled = value;
      } else if (['twoFactorAuth', 'loginAlerts', 'dataSharing', 'analytics', 'locationServices', 'autoRenewal'].includes(key)) {
        (updatedSettings.account as any)[key] = value;
      }

      await updateSettings(updatedSettings);
    }
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Account Deleted', 'Your account has been deleted.');
            // Handle account deletion logic here
          },
        },
      ]
    );
  };

  // Enhanced modal handlers
  const handleAgeRangePress = () => {
    triggerHaptic.light();
    setTempAgeRange({ min: localSettings.ageMin, max: localSettings.ageMax });
    setShowAgeRangeModal(true);
  };

  const handleDistancePress = () => {
    triggerHaptic.light();
    setTempDistance(localSettings.maxDistance);
    setShowDistanceModal(true);
  };

  const handleGenderPress = () => {
    triggerHaptic.light();
    setTempGender(localSettings.genderPreference);
    setShowGenderModal(true);
  };

  const handleQuietHoursPress = () => {
    triggerHaptic.light();
    setTempQuietHours({
      start: localSettings.quietHoursStart,
      end: localSettings.quietHoursEnd
    });
    setShowQuietHoursModal(true);
  };

  const handleSessionTimeoutPress = () => {
    triggerHaptic.light();
    setTempSessionTimeout(localSettings.sessionTimeout || 30);
    setShowSessionTimeoutModal(true);
  };

  // Save handlers for modals
  const saveAgeRange = async () => {
    setLocalSettings(prev => ({
      ...prev,
      ageMin: tempAgeRange.min,
      ageMax: tempAgeRange.max
    }));

    if (settings) {
      const updatedSettings = {
        ...settings,
        dating: {
          ...settings.dating,
          ageRange: { min: tempAgeRange.min, max: tempAgeRange.max }
        }
      };
      await updateSettings(updatedSettings);
    }
  };

  const saveDistance = async () => {
    setLocalSettings(prev => ({ ...prev, maxDistance: tempDistance }));

    if (settings) {
      const updatedSettings = {
        ...settings,
        dating: {
          ...settings.dating,
          maxDistance: tempDistance
        }
      };
      await updateSettings(updatedSettings);
    }
  };

  const saveGender = async () => {
    setLocalSettings(prev => ({ ...prev, genderPreference: tempGender as any }));

    if (settings) {
      const updatedSettings = {
        ...settings,
        dating: {
          ...settings.dating,
          genderPreference: tempGender as any
        }
      };
      await updateSettings(updatedSettings);
    }
  };

  const saveQuietHours = async () => {
    setLocalSettings(prev => ({
      ...prev,
      quietHoursStart: tempQuietHours.start,
      quietHoursEnd: tempQuietHours.end
    }));

    if (settings) {
      const updatedSettings = {
        ...settings,
        notifications: {
          ...settings.notifications,
          quietHours: {
            ...settings.notifications.quietHours,
            startTime: tempQuietHours.start,
            endTime: tempQuietHours.end
          }
        }
      };
      await updateSettings(updatedSettings);
    }
  };

  const saveSessionTimeout = async () => {
    setLocalSettings(prev => ({ ...prev, sessionTimeout: tempSessionTimeout }));

    if (settings) {
      const updatedSettings = {
        ...settings,
        account: {
          ...settings.account,
          sessionTimeout: tempSessionTimeout
        }
      };
      await updateSettings(updatedSettings);
    }
  };

  // Advanced settings handlers
  const handleExportSettings = () => {
    triggerHaptic.medium();

    Alert.alert(
      'Export Settings',
      'Your settings will be exported to a file that you can save or share.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Export', onPress: () => {
          // In production, this would export settings to a file
          Alert.alert('Settings Exported', 'Your settings have been exported successfully.');
        }},
      ]
    );
  };

  const handleImportSettings = () => {
    triggerHaptic.medium();

    Alert.alert(
      'Import Settings',
      'Select a settings file to import your preferences.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Import', onPress: () => {
          // In production, this would open a file picker
          Alert.alert('Import Settings', 'File picker would open here.');
        }},
      ]
    );
  };

  const handleResetSettings = () => {
    triggerHaptic.heavy();

    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to their default values?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Reset', style: 'destructive', onPress: async () => {
          // Reset to default settings
          const defaultSettings = {
            ageMin: 22,
            ageMax: 35,
            maxDistance: 50,
            genderPreference: 'everyone' as const,
            profileVisibility: 'public' as const,
            showDistance: true,
            showAge: true,
            showLastSeen: false,
            allowMessagesFrom: 'matches' as const,
            showOnlineStatus: true,
            incognitoMode: false,
            pushNotifications: true,
            emailNotifications: false,
            newMatches: true,
            newMessages: true,
            likes: true,
            superLikes: true,
            promotions: false,
            tips: true,
            soundEnabled: true,
            vibrationEnabled: true,
            quietHoursEnabled: false,
            quietHoursStart: '22:00',
            quietHoursEnd: '08:00',
            twoFactorAuth: false,
            loginAlerts: true,
            dataSharing: false,
            analytics: true,
            locationServices: true,
            autoRenewal: false,
            sessionTimeout: 30,
          };

          setLocalSettings(defaultSettings);
          Alert.alert('Settings Reset', 'All settings have been reset to default values.');
        }},
      ]
    );
  };

  // Search and filter functions
  const handleSearch = (query: string) => {
    setSearchQuery(query.toLowerCase());
  };

  const handleFilter = (categories: string[]) => {
    setSelectedCategories(categories);
  };

  const shouldShowSection = (sectionName: string, items: string[]) => {
    // If no search query and no filters, show all sections
    if (!searchQuery && selectedCategories.length === 0) {
      return true;
    }

    // If categories are filtered and this section is not selected, hide it
    if (selectedCategories.length > 0 && !selectedCategories.includes(sectionName)) {
      return false;
    }

    // If there's a search query, check if any items in this section match
    if (searchQuery) {
      return items.some(item => item.toLowerCase().includes(searchQuery));
    }

    return true;
  };

  if (isLoading) {
    return (
      <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <LoadingSkeleton variant="settings" />
        </SafeAreaView>
      </LinearGradient>
    );
  }

  const SettingItem = ({
    icon: Icon, 
    title, 
    subtitle, 
    value, 
    onToggle, 
    type = 'switch',
    onPress 
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    value?: boolean;
    onToggle?: (value: boolean) => void;
    type?: 'switch' | 'button';
    onPress?: () => void;
  }) => (
    <TouchableOpacity 
      style={styles.settingItem} 
      onPress={type === 'button' ? onPress : undefined}
      disabled={type === 'switch'}
    >
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          <Icon size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {type === 'switch' && onToggle && (
        <Switch
          value={value}
          onValueChange={onToggle}
          trackColor={{ false: theme.colors.gray300, true: theme.colors.primary + '40' }}
          thumbColor={value ? theme.colors.primary : theme.colors.gray400}
        />
      )}
      {type === 'button' && (
        <ChevronRight size={20} color={theme.colors.gray400} />
      )}
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Settings & Preferences</Text>
          <TouchableOpacity
            style={styles.advancedButton}
            onPress={() => setShowAdvancedSettings(!showAdvancedSettings)}
          >
            <Settings size={24} color="white" />
          </TouchableOpacity>
        </View>

        {/* Search and Filter */}
        <View style={styles.searchContainer}>
          <SettingsSearch
            onSearch={handleSearch}
            onFilter={handleFilter}
            categories={settingsCategories}
            placeholder="Search settings..."
          />
        </View>

        {/* Sync Manager */}
        <View style={styles.syncContainer}>
          <SettingsSyncManager />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Dating Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dating Preferences</Text>
          
          <SettingItem
            icon={Heart}
            title="Age Range"
            subtitle={`${localSettings.ageMin} - ${localSettings.ageMax} years`}
            type="button"
            onPress={handleAgeRangePress}
          />

          <SettingItem
            icon={MapPin}
            title="Maximum Distance"
            subtitle={`${localSettings.maxDistance} km`}
            type="button"
            onPress={handleDistancePress}
          />

          <SettingItem
            icon={User}
            title="Show Me"
            subtitle={localSettings.genderPreference === 'everyone' ? 'Everyone' : localSettings.genderPreference}
            type="button"
            onPress={handleGenderPress}
          />
        </View>

        {/* Privacy Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Safety</Text>
          
          <SettingItem
            icon={Eye}
            title="Show Distance"
            subtitle="Let others see how far away you are"
            value={localSettings.showDistance}
            onToggle={(value) => handleToggle('showDistance', value)}
          />
          
          <SettingItem
            icon={User}
            title="Show Age"
            subtitle="Display your age on your profile"
            value={localSettings.showAge}
            onToggle={(value) => handleToggle('showAge', value)}
          />
          
          <SettingItem
            icon={Eye}
            title="Show Last Seen"
            subtitle="Let others see when you were last active"
            value={localSettings.showLastSeen}
            onToggle={(value) => handleToggle('showLastSeen', value)}
          />
          
          <SettingItem
            icon={MessageCircle}
            title="Allow Messages From"
            subtitle={localSettings.allowMessagesFrom === 'everyone' ? 'Everyone' : 'Matches Only'}
            type="button"
            onPress={() => Alert.alert('Message Settings', 'Message settings coming soon!')}
          />
          
          <SettingItem
            icon={Shield}
            title="Incognito Mode"
            subtitle="Browse profiles privately"
            value={localSettings.incognitoMode}
            onToggle={(value) => handleToggle('incognitoMode', value)}
          />
        </View>

        {/* Notification Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          
          <SettingItem
            icon={Bell}
            title="Push Notifications"
            subtitle="Receive notifications on your device"
            value={localSettings.pushNotifications}
            onToggle={(value) => handleToggle('pushNotifications', value)}
          />
          
          <SettingItem
            icon={Heart}
            title="New Matches"
            subtitle="Get notified when you have a new match"
            value={localSettings.newMatches}
            onToggle={(value) => handleToggle('newMatches', value)}
          />
          
          <SettingItem
            icon={MessageCircle}
            title="New Messages"
            subtitle="Get notified when you receive messages"
            value={localSettings.newMessages}
            onToggle={(value) => handleToggle('newMessages', value)}
          />
          
          <SettingItem
            icon={Volume2}
            title="Sound"
            subtitle="Play sounds for notifications"
            value={localSettings.soundEnabled}
            onToggle={(value) => handleToggle('soundEnabled', value)}
          />
          
          <SettingItem
            icon={Vibrate}
            title="Vibration"
            subtitle="Vibrate for notifications"
            value={localSettings.vibrationEnabled}
            onToggle={(value) => handleToggle('vibrationEnabled', value)}
          />
          
          <SettingItem
            icon={Moon}
            title="Quiet Hours"
            subtitle={localSettings.quietHoursEnabled ? `${localSettings.quietHoursStart} - ${localSettings.quietHoursEnd}` : 'Disabled'}
            value={localSettings.quietHoursEnabled}
            onToggle={(value) => handleToggle('quietHoursEnabled', value)}
          />

          {localSettings.quietHoursEnabled && (
            <SettingItem
              icon={Clock}
              title="Set Quiet Hours"
              subtitle="Configure when to pause notifications"
              type="button"
              onPress={handleQuietHoursPress}
            />
          )}
        </View>

        {/* Account Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account & Security</Text>
          
          <SettingItem
            icon={Shield}
            title="Two-Factor Authentication"
            subtitle="Add an extra layer of security"
            value={localSettings.twoFactorAuth}
            onToggle={(value) => handleToggle('twoFactorAuth', value)}
          />
          
          <SettingItem
            icon={Bell}
            title="Login Alerts"
            subtitle="Get notified of new logins"
            value={localSettings.loginAlerts}
            onToggle={(value) => handleToggle('loginAlerts', value)}
          />
          
          <SettingItem
            icon={MapPin}
            title="Location Services"
            subtitle="Allow location access for better matches"
            value={localSettings.locationServices}
            onToggle={(value) => handleToggle('locationServices', value)}
          />

          <SettingItem
            icon={Clock}
            title="Session Timeout"
            subtitle={`Auto-logout after ${localSettings.sessionTimeout || 30} minutes`}
            type="button"
            onPress={handleSessionTimeoutPress}
          />
        </View>

        {/* Quick Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Settings</Text>

          <SettingItem
            icon={User}
            title="Account Settings"
            subtitle="Email, password, security"
            type="button"
            onPress={() => router.push('/profile/account')}
          />

          <SettingItem
            icon={Shield}
            title="Privacy & Safety"
            subtitle="Profile visibility, blocking"
            type="button"
            onPress={() => router.push('/profile/privacy')}
          />

          <SettingItem
            icon={Settings}
            title="App Preferences"
            subtitle="Language, theme, units"
            type="button"
            onPress={() => router.push('/profile/preferences')}
          />

          <SettingItem
            icon={Award}
            title="Profile Verification"
            subtitle="Verify your identity"
            type="button"
            onPress={() => router.push('/profile/verification')}
          />

          <SettingItem
            icon={Info}
            title="About & Legal"
            subtitle="Terms, privacy policy, support"
            type="button"
            onPress={() => router.push('/profile/about')}
          />
        </View>

        {/* Advanced Settings */}
        {showAdvancedSettings && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Advanced Settings</Text>

            <SettingItem
              icon={Download}
              title="Export Settings"
              subtitle="Save your settings to a file"
              type="button"
              onPress={handleExportSettings}
            />

            <SettingItem
              icon={Upload}
              title="Import Settings"
              subtitle="Load settings from a file"
              type="button"
              onPress={handleImportSettings}
            />

            <SettingItem
              icon={RotateCcw}
              title="Reset to Defaults"
              subtitle="Reset all settings to default values"
              type="button"
              onPress={handleResetSettings}
            />

            {/* Development/Testing Menu Items */}
            {__DEV__ && (
              <>
                <SettingItem
                  icon={Settings}
                  title="Settings Test Suite"
                  subtitle="Test settings functionality"
                  type="button"
                  onPress={() => router.push('/test/settings-suite')}
                />

                <SettingItem
                  icon={TestTube}
                  title="Web Compatibility Test"
                  subtitle="Test web browser compatibility"
                  type="button"
                  onPress={() => setShowWebTest(true)}
                />
              </>
            )}
          </View>
        )}

        {/* Danger Zone */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, styles.dangerTitle]}>Danger Zone</Text>

          <TouchableOpacity style={styles.dangerItem} onPress={handleDeleteAccount}>
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, styles.dangerIcon]}>
                <Trash2 size={20} color={theme.colors.error} />
              </View>
              <View style={styles.settingContent}>
                <Text style={[styles.settingTitle, styles.dangerText]}>Delete Account</Text>
                <Text style={styles.settingSubtitle}>Permanently delete your account and data</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Modals */}
      <SettingsModal
        visible={showAgeRangeModal}
        onClose={() => setShowAgeRangeModal(false)}
        title="Age Range"
        onSave={saveAgeRange}
      >
        <RangePicker
          min={18}
          max={80}
          step={1}
          initialMinValue={tempAgeRange.min}
          initialMaxValue={tempAgeRange.max}
          onValueChange={(min, max) => setTempAgeRange({ min, max })}
          formatLabel={(value) => `${value} years`}
          title="Select Age Range"
        />
      </SettingsModal>

      <SettingsModal
        visible={showDistanceModal}
        onClose={() => setShowDistanceModal(false)}
        title="Maximum Distance"
        onSave={saveDistance}
      >
        <DistancePicker
          initialDistance={tempDistance}
          onDistanceChange={setTempDistance}
          units="km"
          showUnitsToggle={true}
          title="Select Maximum Distance"
        />
      </SettingsModal>

      <SettingsModal
        visible={showGenderModal}
        onClose={() => setShowGenderModal(false)}
        title="Show Me"
        onSave={saveGender}
      >
        <View style={styles.genderOptions}>
          {['everyone', 'men', 'women', 'non-binary'].map((option) => (
            <TouchableOpacity
              key={option}
              style={[
                styles.genderOption,
                tempGender === option && styles.genderOptionSelected,
              ]}
              onPress={() => setTempGender(option)}
            >
              <Text
                style={[
                  styles.genderOptionText,
                  tempGender === option && styles.genderOptionTextSelected,
                ]}
              >
                {option === 'everyone' ? 'Everyone' : option.charAt(0).toUpperCase() + option.slice(1)}
              </Text>
              {tempGender === option && <Check size={20} color="white" />}
            </TouchableOpacity>
          ))}
        </View>
      </SettingsModal>

      <SettingsModal
        visible={showQuietHoursModal}
        onClose={() => setShowQuietHoursModal(false)}
        title="Quiet Hours"
        onSave={saveQuietHours}
      >
        <View style={styles.quietHoursContainer}>
          <View style={styles.timePickerContainer}>
            <Text style={styles.timePickerLabel}>Start Time</Text>
            <TimePicker
              initialTime={tempQuietHours.start}
              onTimeChange={(time) => setTempQuietHours(prev => ({ ...prev, start: time }))}
              title="Start Time"
            />
          </View>
          <View style={styles.timePickerContainer}>
            <Text style={styles.timePickerLabel}>End Time</Text>
            <TimePicker
              initialTime={tempQuietHours.end}
              onTimeChange={(time) => setTempQuietHours(prev => ({ ...prev, end: time }))}
              title="End Time"
            />
          </View>
        </View>
      </SettingsModal>

      <SettingsModal
        visible={showSessionTimeoutModal}
        onClose={() => setShowSessionTimeoutModal(false)}
        title="Session Timeout"
        onSave={saveSessionTimeout}
      >
        <SessionTimeoutPicker
          initialTimeout={tempSessionTimeout}
          onTimeoutChange={setTempSessionTimeout}
          title="Auto-logout after inactivity"
        />
      </SettingsModal>

      {/* Web Compatibility Test Modal */}
      <SettingsModal
        visible={showWebTest}
        onClose={() => setShowWebTest(false)}
        title="Web Compatibility Test"
        showSaveButton={false}
      >
        <WebCompatibilityTest />
      </SettingsModal>

      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  advancedButton: {
    padding: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  syncContainer: {
    paddingHorizontal: 20,
  },
  sectionHeaderTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 12,
  },
  dangerTitle: {
    color: theme.colors.error,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${theme.colors.primary}20`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  dangerIcon: {
    backgroundColor: `${theme.colors.error}20`,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    marginBottom: 2,
  },
  dangerText: {
    color: theme.colors.error,
  },
  settingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  dangerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  // Modal styles
  genderOptions: {
    paddingVertical: 16,
  },
  genderOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginVertical: 4,
    borderRadius: 12,
    backgroundColor: theme.colors.gray100,
  },
  genderOptionSelected: {
    backgroundColor: theme.colors.primary,
  },
  genderOptionText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
  },
  genderOptionTextSelected: {
    color: 'white',
  },
  quietHoursContainer: {
    paddingVertical: 16,
  },
  timePickerContainer: {
    marginBottom: 24,
  },
  timePickerLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 12,
  },
});
