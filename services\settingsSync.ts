import { ProfileSettings } from '../types/profile';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SettingsChangeListener {
  id: string;
  callback: (settings: ProfileSettings) => void;
  sections?: string[]; // Optional: only listen to specific sections
}

interface SettingsConflict {
  key: string;
  localValue: any;
  remoteValue: any;
  timestamp: Date;
}

class SettingsSyncService {
  private listeners: SettingsChangeListener[] = [];
  private syncQueue: Array<{ settings: ProfileSettings; timestamp: Date }> = [];
  private isOnline: boolean = true;
  private lastSyncTimestamp: Date = new Date();
  private conflictResolutionStrategy: 'local' | 'remote' | 'latest' = 'latest';

  // Storage keys
  private readonly SETTINGS_KEY = '@settings';
  private readonly SETTINGS_BACKUP_KEY = '@settings_backup';
  private readonly SYNC_TIMESTAMP_KEY = '@sync_timestamp';
  private readonly SETTINGS_HISTORY_KEY = '@settings_history';

  constructor() {
    this.initializeNetworkListener();
    this.loadCachedSettings();
  }

  /**
   * Register a listener for settings changes
   */
  addListener(listener: SettingsChangeListener): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l.id !== listener.id);
    };
  }

  /**
   * Update settings with real-time sync
   */
  async updateSettings(
    settings: ProfileSettings, 
    source: string = 'unknown',
    skipSync: boolean = false
  ): Promise<void> {
    try {
      // Add to history for audit trail
      await this.addToHistory(settings, source);

      // Cache locally
      await this.cacheSettings(settings);

      // Add to sync queue if offline
      if (!this.isOnline && !skipSync) {
        this.syncQueue.push({ settings, timestamp: new Date() });
      }

      // Sync to remote if online
      if (this.isOnline && !skipSync) {
        await this.syncToRemote(settings);
      }

      // Notify all listeners
      this.notifyListeners(settings);

    } catch (error) {
      console.error('Failed to update settings:', error);
      throw error;
    }
  }

  /**
   * Get current settings with fallback to cache
   */
  async getSettings(): Promise<ProfileSettings | null> {
    try {
      // Try to get from remote first if online
      if (this.isOnline) {
        const remoteSettings = await this.fetchFromRemote();
        if (remoteSettings) {
          await this.cacheSettings(remoteSettings);
          return remoteSettings;
        }
      }

      // Fallback to cached settings
      return await this.getCachedSettings();
    } catch (error) {
      console.error('Failed to get settings:', error);
      return await this.getCachedSettings();
    }
  }

  /**
   * Export settings to JSON
   */
  async exportSettings(): Promise<string> {
    const settings = await this.getSettings();
    const exportData = {
      settings,
      exportedAt: new Date().toISOString(),
      version: '1.0',
    };
    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Import settings from JSON
   */
  async importSettings(jsonData: string): Promise<void> {
    try {
      const importData = JSON.parse(jsonData);
      
      if (!importData.settings) {
        throw new Error('Invalid settings file format');
      }

      // Validate settings structure
      this.validateSettings(importData.settings);

      // Update with imported settings
      await this.updateSettings(importData.settings, 'import');
    } catch (error) {
      console.error('Failed to import settings:', error);
      throw new Error('Failed to import settings: ' + error.message);
    }
  }

  /**
   * Reset settings to defaults
   */
  async resetToDefaults(): Promise<void> {
    const defaultSettings = this.getDefaultSettings();
    await this.updateSettings(defaultSettings, 'reset');
  }

  /**
   * Create backup of current settings
   */
  async createBackup(): Promise<void> {
    const settings = await this.getSettings();
    if (settings) {
      const backup = {
        settings,
        timestamp: new Date().toISOString(),
      };
      await AsyncStorage.setItem(this.SETTINGS_BACKUP_KEY, JSON.stringify(backup));
    }
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(): Promise<void> {
    try {
      const backupData = await AsyncStorage.getItem(this.SETTINGS_BACKUP_KEY);
      if (backupData) {
        const backup = JSON.parse(backupData);
        await this.updateSettings(backup.settings, 'restore');
      } else {
        throw new Error('No backup found');
      }
    } catch (error) {
      console.error('Failed to restore from backup:', error);
      throw error;
    }
  }

  /**
   * Get settings change history
   */
  async getSettingsHistory(): Promise<Array<{ settings: ProfileSettings; source: string; timestamp: Date }>> {
    try {
      const historyData = await AsyncStorage.getItem(this.SETTINGS_HISTORY_KEY);
      return historyData ? JSON.parse(historyData) : [];
    } catch (error) {
      console.error('Failed to get settings history:', error);
      return [];
    }
  }

  /**
   * Handle conflicts when syncing
   */
  private async resolveConflicts(
    localSettings: ProfileSettings,
    remoteSettings: ProfileSettings
  ): Promise<ProfileSettings> {
    const conflicts: SettingsConflict[] = [];
    
    // Deep compare settings and identify conflicts
    this.findConflicts(localSettings, remoteSettings, '', conflicts);

    if (conflicts.length === 0) {
      return remoteSettings;
    }

    // Apply conflict resolution strategy
    switch (this.conflictResolutionStrategy) {
      case 'local':
        return localSettings;
      case 'remote':
        return remoteSettings;
      case 'latest':
      default:
        return this.mergeByTimestamp(localSettings, remoteSettings);
    }
  }

  private findConflicts(
    local: any,
    remote: any,
    path: string,
    conflicts: SettingsConflict[]
  ): void {
    for (const key in local) {
      const currentPath = path ? `${path}.${key}` : key;
      
      if (typeof local[key] === 'object' && typeof remote[key] === 'object') {
        this.findConflicts(local[key], remote[key], currentPath, conflicts);
      } else if (local[key] !== remote[key]) {
        conflicts.push({
          key: currentPath,
          localValue: local[key],
          remoteValue: remote[key],
          timestamp: new Date(),
        });
      }
    }
  }

  private mergeByTimestamp(
    localSettings: ProfileSettings,
    remoteSettings: ProfileSettings
  ): ProfileSettings {
    // For now, prefer remote settings
    // In production, this would use actual timestamps
    return remoteSettings;
  }

  private async cacheSettings(settings: ProfileSettings): Promise<void> {
    await AsyncStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
    await AsyncStorage.setItem(this.SYNC_TIMESTAMP_KEY, new Date().toISOString());
  }

  private async getCachedSettings(): Promise<ProfileSettings | null> {
    try {
      const settingsData = await AsyncStorage.getItem(this.SETTINGS_KEY);
      return settingsData ? JSON.parse(settingsData) : null;
    } catch (error) {
      console.error('Failed to get cached settings:', error);
      return null;
    }
  }

  private async syncToRemote(settings: ProfileSettings): Promise<void> {
    // In production, this would sync to your backend
    console.log('Syncing settings to remote:', settings);
    this.lastSyncTimestamp = new Date();
  }

  private async fetchFromRemote(): Promise<ProfileSettings | null> {
    // In production, this would fetch from your backend
    console.log('Fetching settings from remote');
    return null;
  }

  private notifyListeners(settings: ProfileSettings): void {
    this.listeners.forEach(listener => {
      try {
        listener.callback(settings);
      } catch (error) {
        console.error('Error in settings listener:', error);
      }
    });
  }

  private async addToHistory(settings: ProfileSettings, source: string): Promise<void> {
    try {
      const history = await this.getSettingsHistory();
      const newEntry = {
        settings,
        source,
        timestamp: new Date(),
      };
      
      // Keep only last 50 entries
      const updatedHistory = [newEntry, ...history].slice(0, 50);
      await AsyncStorage.setItem(this.SETTINGS_HISTORY_KEY, JSON.stringify(updatedHistory));
    } catch (error) {
      console.error('Failed to add to history:', error);
    }
  }

  private validateSettings(settings: any): void {
    // Basic validation - in production, use a schema validator
    if (!settings || typeof settings !== 'object') {
      throw new Error('Settings must be an object');
    }
    
    const requiredSections = ['dating', 'privacy', 'notifications', 'account'];
    for (const section of requiredSections) {
      if (!settings[section]) {
        throw new Error(`Missing required section: ${section}`);
      }
    }
  }

  private getDefaultSettings(): ProfileSettings {
    // Return default settings structure
    return {
      dating: {
        ageRange: { min: 22, max: 35 },
        maxDistance: 50,
        genderPreference: 'everyone',
        showMe: 'everyone',
        dealBreakers: [],
        mustHaves: [],
      },
      privacy: {
        profileVisibility: 'public',
        showDistance: true,
        showAge: true,
        showLastSeen: false,
        allowMessagesFrom: 'matches',
        showOnlineStatus: true,
        incognitoMode: false,
      },
      notifications: {
        pushNotifications: true,
        emailNotifications: false,
        newMatches: true,
        newMessages: true,
        likes: true,
        superLikes: true,
        promotions: false,
        tips: true,
        soundEnabled: true,
        vibrationEnabled: true,
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00',
        },
      },
      account: {
        twoFactorAuth: false,
        loginAlerts: true,
        dataSharing: false,
        analytics: true,
        locationServices: true,
        autoRenewal: false,
        emailVerified: false,
        phoneVerified: false,
        sessionTimeout: 30,
        deviceManagement: true,
      },
      app: {
        language: 'en',
        theme: 'system',
        units: 'metric',
        currency: 'USD',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        autoPlayVideos: true,
        reducedMotion: false,
        highContrast: false,
        fontSize: 'medium',
      },
    };
  }

  private initializeNetworkListener(): void {
    // In production, use NetInfo to listen for network changes
    // For now, assume always online
    this.isOnline = true;
  }

  private async loadCachedSettings(): Promise<void> {
    // Load any cached settings on initialization
    const cached = await this.getCachedSettings();
    if (cached) {
      this.notifyListeners(cached);
    }
  }
}

// Export singleton instance
export const settingsSyncService = new SettingsSyncService();
export default settingsSyncService;
